using System;
using Bibliotech.Core.Abstractions;
using Bibliotech.Core.Commands.Users;
using Bibliotech.Core.Entities;
using Bibliotech.Core.Repositories;
using Bibliotech.Core.Services;
using Microsoft.Extensions.Logging;

namespace Bibliotech.Modules.Infrastructure.Handlers.Users;

public class CreateUserCommandHandler : I<PERSON>ommandHandler<CreateUserCommand, Result<CreateUserResponse>>
{

          private readonly IUserRepository _userRepository;
          private readonly IPasswordService _passwordService;
          private readonly ILogger<CreateUserCommandHandler> _logger;

          public CreateUserCommandHandler(IUserRepository userRepository, IPasswordService passwordService, ILogger<CreateUserCommandHandler> logger)
          {
                    _userRepository = userRepository;
                    _passwordService = passwordService;
                    _logger = logger;         
          }

          public async Task<Result<CreateUserResponse>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
          {
                    try
                    {
                              if (await _userRepository.ExistsByEmailAsync(request.Email, cancellationToken))
                              {
                                        return Result<CreateUserResponse>.Failure("User with this email already exists");
                              }

                              var passwordHash = _passwordService.HashPassword(request.Password);

                              var user = User.Create(request.Email, passwordHash, request.FirstName, request.LastName);
                              if (await _userRepository.AnyUsers())
                              {
                                        user.AddRole("User");
                              }
                              else
                              {
                                        user.AddRole("Admin");
                              }
                              await _userRepository.AddAsync(user, cancellationToken);

                              _logger.LogInformation("User created successfully: {Email}", request.Email);

                              var response = new CreateUserResponse(
                                        user.Id.Value.ToString(),
                                        user.Email,
                                        user.FirstName,
                                        user.LastName,
                                        user.CreatedAt
                              );

                              return Result<CreateUserResponse>.Success(response);
                    }
                    catch(Exception ex)
                    {
                              _logger.LogError(ex, "Error creating user with email {Email}", request.Email);
                              return Result<CreateUserResponse>.Failure("An error occured while creating the user");
                    }
          }
}