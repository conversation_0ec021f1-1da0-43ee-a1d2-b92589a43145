using System;
using Amazon.Runtime.Internal.Util;
using Bibliotech.Core.Abstractions;
using Bibliotech.Core.Commands.Users;
using Bibliotech.Core.Repositories;
using Bibliotech.Core.ValueObjects;
using Microsoft.Extensions.Logging;

namespace Bibliotech.Modules.Infrastructure.Handlers;

public class GetUserByIdQueryHandler : IQueryHandler<GetUserByIdQuery, Result<UserDto>>
{
          private readonly IUserRepository _userRepository;
          private readonly ILogger<GetUserByIdQueryHandler> _logger;


          public GetUserByIdQueryHandler(IUserRepository userRepository, ILogger<GetUserByIdQueryHandler> logger)
          {
                    _userRepository = userRepository;
                    _logger = logger;
          }
          public async Task<Result<UserDto>> Handle(GetUserByIdQuery request, CancellationToken cancellationToken)
          {
                    try
                    {
                              if (!Guid.TryParse(request.UserId, out var userGuid))
                              {
                                        return Result<UserDto>.Failure("Invalid user ID format");
                              }

                              var userId = UserId.From(userGuid);
                              var user = await _userRepository.GetByIdAsync(userId, cancellationToken);

                              if (user == null)
                              {
                                        return Result<UserDto>.Failure("User not found");
                              }

                              var userDto = new UserDto(
                                        user.Id.Value.ToString(),
                                        user.Email,
                                        user.FirstName,
                                        user.LastName,
                                        user.IsEmailVerified,
                                        user.Roles.ToList()
                              );

                              return Result<UserDto>.Success(userDto);
                    }
                    catch (Exception ex)
                    {
                              _logger.LogError(ex, "Error retrieving user with ID {UserId}", request.UserId);
                              return Result<UserDto>.Failure("An error occured while retrieving the user");
                    }
          }
}
