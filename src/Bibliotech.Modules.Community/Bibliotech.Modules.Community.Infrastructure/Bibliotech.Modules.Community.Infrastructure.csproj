﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../../Bibliotech.Core/Bibliotech.Core.csproj" />
    <ProjectReference Include="../Bibliotech.Modules.Community.Application/Bibliotech.Modules.Community.Application.csproj" />
    <ProjectReference Include="../Bibliotech.Modules.Community.Domain/Bibliotech.Modules.Community.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations/" />
    <Folder Include="Configurations/" />
    <Folder Include="Repositories/" />
    <Folder Include="External/" />
    <Folder Include="Caching/" />
    <Folder Include="Services/" />
  </ItemGroup>

</Project>
