<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="8.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.17" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.17" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.17" />
    <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.2" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="../Bibliotech.Core/Bibliotech.Core.csproj" />
    <ProjectReference Include="../Bibliotech.Modules.Infrastructure/Bibliotech.Modules.Infrastructure.csproj" />
  </ItemGroup>
</Project>