{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=Bibliotech;Username=postgres;Password=*****", "MongoDb": "mongodb+srv://kushaltmg321:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}, "DatabaseSettings": {"PostgreSqlConnection": "Host=localhost;Database=Bibliotech;Username=postgres;Password=*****", "MongoDbConnection": "mongodb+srv://kushaltmg321:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0", "MongoDbDatabaseName": "Bibliotech", "CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false}, "JwtSettings": {"SecretKey": "asgda732d78agudwgd87gwqwdgG7asdh89HH989ua898y98we8y982wefywe89yyrf3y8yu7r34yr83y8wey8weyr8er", "Issuer": "Bibliotech.API", "Audience": "Bibliotech.Client", "AccessTokenExpirationMinutes": 15, "RefreshTokenExpirationDays": 7, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true}}