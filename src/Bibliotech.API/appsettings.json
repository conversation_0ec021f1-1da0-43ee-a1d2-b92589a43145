{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=Bibliotech;Username=postgres;Password=*****", "MongoDb": "mongodb+srv://kushal:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}, "DatabaseSettings": {"PostgreSqlConnection": "Host=localhost;Database=Bibliotech;Username=postgres;Password=*****", "MongoDbConnection": "mongodb+srv://kushal:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0", "MongoDbDatabaseName": "Bibliotech", "CommandTimeout": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": false}}