﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="../../Bibliotech.Core/Bibliotech.Core.csproj" />
    <ProjectReference Include="../Bibliotech.Modules.Enterprise.Domain/Bibliotech.Modules.Enterprise.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Commands/" />
    <Folder Include="Queries/" />
    <Folder Include="Handlers/" />
    <Folder Include="Services/" />
    <Folder Include="Interfaces/" />
    <Folder Include="DTOs/" />
    <Folder Include="Validators/" />
    <Folder Include="Behaviours/" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
  </ItemGroup>

</Project>
